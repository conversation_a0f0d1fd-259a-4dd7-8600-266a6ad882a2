# CM311-1E 卡刷包签名工具使用说明

**作者**: By.举个🌰  
**版权所有**: © 2025  
**版本**: v1.0

## 🎯 工具简介

CM311-1E卡刷包签名工具是专门为Recovery刷机包设计的签名工具，解决了之前APK签名工具在处理卡刷包时出现的问题。

### ✅ 主要特性

- **专用于卡刷包**: 针对Recovery刷机包优化
- **标准签名格式**: 生成标准的Android签名
- **无重复文件**: 解决MANIFEST.MF重复问题
- **自动验证**: 签名后自动验证完整性
- **Recovery密钥**: 自动生成匹配的验证密钥
- **U盘刷机**: 支持U盘刷机格式

### 🔧 解决的问题

1. **重复MANIFEST.MF文件** - 之前导致刷机失败的主要原因
2. **签名格式不标准** - 确保Recovery能正确验证
3. **密钥不匹配** - 自动生成匹配的验证密钥
4. **文件结构混乱** - 重建ZIP确保结构正确

## 📋 使用步骤

### 第一步：生成Recovery密钥

1. 启动工具：`python "CM311-1E卡刷包签名工具.py"`
2. 在"Recovery密钥生成"区域：
   - 设置密钥大小（推荐2048位）
   - 选择输出目录
   - 点击"生成Recovery密钥"
3. 生成的文件：
   - `recovery_private_key.pem` - 私钥文件
   - `recovery_certificate.pem` - 证书文件
   - `recovery_keys` - Recovery验证密钥

### 第二步：签名卡刷包

1. 在"卡刷包签名"区域：
   - 选择要签名的.zip卡刷包
   - 选择私钥文件（recovery_private_key.pem）
   - 选择证书文件（recovery_certificate.pem）
   - 设置输出文件路径
   - 点击"签名卡刷包"
2. 生成的文件：
   - 签名后的卡刷包（如：update_signed.zip）
   - Recovery验证密钥文件（如：update_recovery_keys）

### 第三步：刷机使用

1. 将签名后的卡刷包复制到U盘根目录
2. 进入Recovery模式
3. 选择"从外部存储安装"或"Install from USB"
4. 选择签名包进行刷机

## 🔍 验证方法

### 检查签名文件结构

```python
import zipfile
with zipfile.ZipFile('your_signed_package.zip', 'r') as zf:
    meta_files = [f for f in zf.namelist() if f.startswith('META-INF/')]
    manifest_files = [f for f in zf.namelist() if f.endswith('MANIFEST.MF')]
    print(f'META-INF文件数: {len(meta_files)}')  # 应该是3
    print(f'MANIFEST.MF数量: {len(manifest_files)}')  # 应该是1
```

### 正确的签名结构

```
META-INF/
├── MANIFEST.MF     (文件清单)
├── CERT.SF         (签名文件)
└── CERT.RSA        (证书文件)
```

## ⚠️ 注意事项

### 文件要求

- **输入文件**: 必须是.zip格式的卡刷包
- **文件路径**: 避免包含空格和特殊字符
- **文件大小**: 确保有足够的磁盘空间

### 刷机要求

- **Recovery版本**: 需要支持自定义签名的Recovery
- **Bootloader**: 设备bootloader必须已解锁
- **设备匹配**: 确保卡刷包与设备型号匹配
- **备份数据**: 刷机前务必备份重要数据

### 密钥管理

- **私钥安全**: 妥善保管私钥文件，不要泄露
- **密钥匹配**: 使用同一套密钥签名的包才能互相覆盖
- **密钥备份**: 建议备份密钥文件到安全位置

## 🚨 故障排除

### 刷机失败的可能原因

1. **Recovery不支持自定义签名**
   - 解决方案：刷入支持自定义签名的Recovery
   - 或者：替换Recovery中的验证密钥

2. **设备bootloader未解锁**
   - 解决方案：按照设备说明解锁bootloader

3. **签名密钥不匹配**
   - 解决方案：使用正确的Recovery验证密钥

4. **卡刷包格式错误**
   - 解决方案：检查updater-script和update-binary

### 常见错误信息

- `signature verification failed` - 签名验证失败
- `installation aborted` - 安装中止
- `bad package` - 包格式错误

### 解决步骤

1. **重新签名**: 使用新工具重新签名卡刷包
2. **检查结构**: 确认只有1个MANIFEST.MF文件
3. **验证密钥**: 确保Recovery密钥正确
4. **更换Recovery**: 使用支持自定义签名的Recovery

## 📁 文件说明

### 生成的密钥文件

- `recovery_private_key.pem`: RSA私钥，用于签名
- `recovery_certificate.pem`: X.509证书，包含公钥
- `recovery_keys`: Recovery验证密钥，v4格式

### 签名后的文件

- `*_signed.zip`: 签名后的卡刷包
- `*_recovery_keys`: 对应的Recovery验证密钥

## 🎉 成功标志

### 签名成功的标志

- ✅ 只有3个META-INF文件
- ✅ 只有1个MANIFEST.MF文件
- ✅ Recovery密钥格式正确（v4格式）
- ✅ 数字不包含引号
- ✅ 签名验证通过

### 刷机成功的标志

- ✅ Recovery能识别签名包
- ✅ 签名验证通过
- ✅ 安装过程正常
- ✅ 设备正常启动

## 💡 最佳实践

1. **测试环境**: 先在测试设备上验证
2. **备份原版**: 保留原始未签名的卡刷包
3. **版本管理**: 为不同版本使用不同的密钥
4. **文档记录**: 记录密钥和签名的对应关系

## 📞 技术支持

如果遇到问题，请检查：
1. 工具版本是否最新
2. 文件路径是否正确
3. 设备是否支持自定义签名
4. Recovery版本是否兼容

---

**免责声明**: 刷机有风险，操作需谨慎。作者不承担因使用本工具导致的任何设备损坏或数据丢失。
