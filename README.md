# CPIO Ramdisk 解包打包工具

**By.举个🌰**
**Copyright © 2025**

一个专门用于处理Android Recovery Ramdisk的CPIO解包打包工具。支持对`ramdisk.cpio`文件进行安全的解包和重新打包操作，保持文件系统结构完整性。

## 功能特性

- ✅ **安全解包**: 完整解包CPIO文件，保留所有文件属性和权限
- ✅ **精确打包**: 重新打包时保持与原始文件格式一致
- ✅ **符号链接支持**: 正确处理符号链接（Windows下转换为.symlink文件）
- ✅ **自动备份**: 操作前自动创建备份文件
- ✅ **GUI界面**: 友好的图形用户界面
- ✅ **进度显示**: 实时显示操作进度和日志
- ✅ **格式验证**: 自动检测和验证CPIO文件格式
- ✅ **测试验证**: 包含完整的测试和分析工具

## 系统要求

- Windows 10/11
- Python 3.6 或更高版本
- WSL (Windows Subsystem for Linux) - 可选，用于更好的文件格式检测

## 安装和使用

### 1. 环境准备

确保已安装Python 3.6+：

```bash
python --version
```

### 2. 运行工具

#### 图形界面模式（推荐）

```bash
python 解包打包.py
```

#### 测试模式

```bash
python test_cpio.py
```

### 3. 使用说明

#### 解包操作

1. 点击"浏览"按钮选择要解包的CPIO文件
2. 选择或输入解包输出目录
3. 点击"开始解包"按钮
4. 等待操作完成，查看日志输出

#### 打包操作

1. 点击"浏览"按钮选择要打包的目录
2. 选择或输入输出CPIO文件路径
3. 点击"开始打包"按钮
4. 等待操作完成，查看日志输出

## 文件结构

```text
项目目录/
├── 解包打包.py          # 主程序文件（GUI界面）
├── analyze_cpio.py      # CPIO文件格式分析工具
├── test_fix.py          # 功能测试脚本
├── README.md            # 说明文档
├── venv/                # Python虚拟环境
└── recovery/
    └── ramdisk.cpio     # 原始ramdisk文件
```

## 使用方法

### 1. 环境准备

```bash
# 激活虚拟环境（如果使用）
venv\Scripts\activate
```

## 技术细节

### CPIO格式支持

- **New ASCII Format (070701)**: 标准的新ASCII格式
- **New CRC Format (070702)**: 带CRC校验的新格式
- **文件类型**: 普通文件、目录、符号链接
- **权限保持**: 保持原始文件权限（在支持的系统上）

### 安全特性

- **自动备份**: 操作前自动创建原始文件备份
- **完整性检查**: 解包后验证文件结构
- **错误恢复**: 操作失败时提供详细错误信息
- **非破坏性**: 不会修改原始文件，除非明确指定

### 符号链接处理

在Windows系统上，由于符号链接支持限制：
- 符号链接会被保存为 `.symlink` 文件
- 文件内容为链接目标路径
- 重新打包时会正确恢复为符号链接

## 常见问题

### Q: 解包后文件权限丢失怎么办？
A: 在Windows系统上这是正常现象，重新打包时会使用默认权限。在Linux系统上权限会被保持。

### Q: 符号链接无法创建怎么办？
A: 工具会自动创建 `.symlink` 文件来记录链接信息，重新打包时会正确处理。

### Q: 文件大小不一致怎么办？
A: 轻微的大小差异是正常的，主要由于对齐和填充。如果差异超过5%，请检查日志中的错误信息。

### Q: 支持哪些CPIO格式？
A: 目前支持 New ASCII (070701) 和 New CRC (070702) 格式，这是Android系统中最常用的格式。

## 开发信息

### 核心类

- `CPIOHandler`: 处理CPIO文件的核心逻辑
- `CPIOToolGUI`: 图形用户界面

### 主要方法

- `extract_cpio()`: 解包CPIO文件
- `create_cpio()`: 创建CPIO文件
- `read_cpio_header()`: 读取CPIO头部信息
- `_write_cpio_entry()`: 写入CPIO条目

## 许可证

本项目仅供学习和研究使用。

## 更新日志

### v1.0.0 (2025-01-XX)
- 初始版本发布
- 支持基本的解包打包功能
- 图形用户界面
- 自动备份功能

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目作者: 举个🌰

---

**注意**: 本工具专门针对Android Recovery Ramdisk设计，使用前请确保备份重要数据。
