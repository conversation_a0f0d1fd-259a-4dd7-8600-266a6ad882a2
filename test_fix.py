#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的CPIO打包功能
By.举个🌰
Copyright © 2025
"""

import os
import sys
import tempfile
import shutil

# 导入CPIO处理器
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
from 解包打包 import CPIOHandler

def test_fixed_packing():
    """测试修复后的打包功能"""
    print("测试修复后的CPIO打包功能")
    print("=" * 50)
    
    # 检查原始文件
    original_file = "recovery/ramdisk.cpio"
    if not os.path.exists(original_file):
        print(f"错误: 找不到原始文件 {original_file}")
        return False
        
    print(f"原始文件: {original_file}")
    print(f"原始大小: {os.path.getsize(original_file):,} 字节")
    
    # 创建临时目录
    with tempfile.TemporaryDirectory() as temp_dir:
        extract_dir = os.path.join(temp_dir, "extracted")
        repack_file = os.path.join(temp_dir, "repacked.cpio")
        
        handler = CPIOHandler()
        
        # 解包
        print("\n1. 解包原始文件...")
        success, message = handler.extract_cpio(original_file, extract_dir)
        if not success:
            print(f"解包失败: {message}")
            return False
        print(f"解包成功: {message}")
        
        # 重新打包
        print("\n2. 重新打包...")
        success, message = handler.create_cpio(extract_dir, repack_file)
        if not success:
            print(f"打包失败: {message}")
            return False
        print(f"打包成功: {message}")
        
        # 比较文件
        if os.path.exists(repack_file):
            repack_size = os.path.getsize(repack_file)
            print(f"\n3. 文件大小比较:")
            print(f"   原始: {os.path.getsize(original_file):,} 字节")
            print(f"   重包: {repack_size:,} 字节")
            
            # 复制到当前目录以便检查
            output_file = "ramdisk_fixed.cpio"
            shutil.copy2(repack_file, output_file)
            print(f"   已复制到: {output_file}")
            
            # 比较头部
            print(f"\n4. 头部比较:")
            with open(original_file, 'rb') as f1, open(repack_file, 'rb') as f2:
                orig_header = f1.read(110)
                new_header = f2.read(110)
                
            print("原始头部:", orig_header[:50].hex())
            print("新建头部:", new_header[:50].hex())
            
            if orig_header[:6] == new_header[:6]:
                print("✓ 魔数匹配")
            else:
                print("✗ 魔数不匹配")
                
            return True
        else:
            print("错误: 重新打包的文件不存在")
            return False

if __name__ == "__main__":
    test_fixed_packing()
