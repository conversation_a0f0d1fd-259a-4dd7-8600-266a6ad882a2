#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析CPIO文件格式
By.举个🌰
Copyright © 2025
"""

import os
import struct

def analyze_cpio_file(filepath):
    """分析CPIO文件格式"""
    if not os.path.exists(filepath):
        print(f"文件不存在: {filepath}")
        return
        
    print(f"分析文件: {filepath}")
    print(f"文件大小: {os.path.getsize(filepath):,} 字节")
    print("-" * 60)
    
    with open(filepath, 'rb') as f:
        data = f.read()
        
    # 分析前几个条目
    offset = 0
    entry_count = 0
    
    while offset < len(data) and entry_count < 10:
        print(f"\n=== 条目 {entry_count + 1} (偏移: 0x{offset:08x}) ===")
        
        # 检查是否有足够的数据读取头部
        if offset + 110 > len(data):
            print("数据不足，无法读取完整头部")
            break
            
        # 读取头部数据
        header_data = data[offset:offset + 110]
        
        # 显示原始头部数据
        print("头部数据 (hex):")
        for i in range(0, len(header_data), 16):
            chunk = header_data[i:i+16]
            hex_str = ' '.join(f'{b:02x}' for b in chunk)
            ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in chunk)
            print(f"  {i:04x}: {hex_str:<48} |{ascii_str}|")
            
        # 解析魔数
        magic = header_data[:6]
        print(f"\n魔数: {magic} ({magic.decode('ascii', errors='ignore')})")
        
        if magic not in [b'070701', b'070702']:
            print("未识别的魔数，可能不是标准CPIO格式")
            break
            
        try:
            # 解析头部字段
            header = {
                'magic': magic.decode('ascii'),
                'ino': int(header_data[6:14], 16),
                'mode': int(header_data[14:22], 16),
                'uid': int(header_data[22:30], 16),
                'gid': int(header_data[30:38], 16),
                'nlink': int(header_data[38:46], 16),
                'mtime': int(header_data[46:54], 16),
                'filesize': int(header_data[54:62], 16),
                'devmajor': int(header_data[62:70], 16),
                'devminor': int(header_data[70:78], 16),
                'rdevmajor': int(header_data[78:86], 16),
                'rdevminor': int(header_data[86:94], 16),
                'namesize': int(header_data[94:102], 16),
                'check': int(header_data[102:110], 16)
            }
            
            print(f"inode: {header['ino']}")
            print(f"mode: 0o{header['mode']:o} (0x{header['mode']:x})")
            print(f"uid: {header['uid']}")
            print(f"gid: {header['gid']}")
            print(f"nlink: {header['nlink']}")
            print(f"mtime: {header['mtime']}")
            print(f"filesize: {header['filesize']}")
            print(f"namesize: {header['namesize']}")
            
            # 读取文件名
            offset += 110
            name_end = offset + header['namesize']
            if name_end > len(data):
                print("数据不足，无法读取文件名")
                break
                
            filename = data[offset:name_end - 1].decode('utf-8', errors='ignore')
            print(f"文件名: '{filename}'")
            
            # 4字节对齐
            offset = (name_end + 3) & ~3
            
            # 跳过文件内容
            file_end = offset + header['filesize']
            if file_end > len(data):
                print("数据不足，无法跳过文件内容")
                break
                
            # 显示文件内容的前几个字节（如果是文本）
            if header['filesize'] > 0 and header['filesize'] < 1000:
                content = data[offset:file_end]
                if all(32 <= b <= 126 or b in [9, 10, 13] for b in content[:100]):
                    print(f"文件内容预览: {content[:100].decode('utf-8', errors='ignore')!r}")
                else:
                    print(f"文件内容 (hex): {content[:32].hex()}")
            
            # 4字节对齐
            offset = (file_end + 3) & ~3
            
            # 检查是否是结束标记
            if filename == 'TRAILER!!!':
                print("遇到结束标记，分析完成")
                break
                
            entry_count += 1
            
        except ValueError as e:
            print(f"解析头部时出错: {e}")
            break
            
    print(f"\n总共分析了 {entry_count} 个条目")

def compare_files(file1, file2):
    """比较两个文件的差异"""
    if not os.path.exists(file1) or not os.path.exists(file2):
        print("文件不存在，无法比较")
        return
        
    print(f"\n比较文件:")
    print(f"  原始: {file1} ({os.path.getsize(file1):,} 字节)")
    print(f"  新建: {file2} ({os.path.getsize(file2):,} 字节)")
    
    with open(file1, 'rb') as f1, open(file2, 'rb') as f2:
        data1 = f1.read()
        data2 = f2.read()
        
    # 比较前1024字节
    min_len = min(len(data1), len(data2), 1024)
    
    print(f"\n前{min_len}字节比较:")
    differences = 0
    for i in range(min_len):
        if data1[i] != data2[i]:
            if differences < 20:  # 只显示前20个差异
                print(f"  偏移 0x{i:04x}: 原始=0x{data1[i]:02x} 新建=0x{data2[i]:02x}")
            differences += 1
            
    if differences == 0:
        print("  前1024字节完全相同")
    else:
        print(f"  发现 {differences} 个字节差异")

def main():
    """主函数"""
    print("CPIO文件格式分析工具")
    print("By.举个🌰")
    print("Copyright © 2025")
    print("=" * 60)
    
    # 分析原始文件
    original_file = "recovery/ramdisk.cpio"
    analyze_cpio_file(original_file)
    
    # 如果存在新打包的文件，也分析它
    new_file = "ramdisk_fixed.cpio"
    if os.path.exists(new_file):
        print("\n" + "=" * 60)
        analyze_cpio_file(new_file)

        # 比较两个文件
        print("\n" + "=" * 60)
        compare_files(original_file, new_file)

if __name__ == "__main__":
    main()
