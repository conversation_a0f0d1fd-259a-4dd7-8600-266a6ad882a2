#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CPIO工具使用示例
By.举个🌰
Copyright © 2025
"""

import os
import sys

def main():
    """主函数 - 显示使用示例"""
    print("=" * 60)
    print("CPIO Ramdisk 解包打包工具 - 使用示例")
    print("By.举个🌰")
    print("Copyright © 2025")
    print("=" * 60)
    
    print("\n📋 可用的操作:")
    print("1. 启动GUI工具")
    print("2. 分析CPIO文件格式")
    print("3. 运行功能测试")
    print("4. 查看帮助信息")
    print("0. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (0-4): ").strip()
            
            if choice == "0":
                print("再见！")
                break
            elif choice == "1":
                print("\n🚀 启动GUI工具...")
                os.system("python 解包打包.py")
            elif choice == "2":
                print("\n🔍 分析CPIO文件格式...")
                os.system("python analyze_cpio.py")
            elif choice == "3":
                print("\n🧪 运行功能测试...")
                os.system("python test_fix.py")
            elif choice == "4":
                show_help()
            else:
                print("❌ 无效选择，请输入0-4之间的数字")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

def show_help():
    """显示帮助信息"""
    print("\n" + "=" * 60)
    print("📖 帮助信息")
    print("=" * 60)
    
    print("\n🎯 主要功能:")
    print("• 解包: 将ramdisk.cpio文件解包到目录")
    print("• 打包: 将目录重新打包为.cpio文件")
    print("• 分析: 查看CPIO文件的内部结构")
    print("• 测试: 验证解包打包功能是否正常")
    
    print("\n📁 重要文件:")
    print("• recovery/ramdisk.cpio - 原始ramdisk文件")
    print("• 解包打包.py - 主程序（GUI界面）")
    print("• analyze_cpio.py - 文件格式分析工具")
    print("• test_fix.py - 功能测试脚本")
    
    print("\n⚠️ 注意事项:")
    print("• 工具会自动备份原始文件")
    print("• Windows下符号链接会转换为.symlink文件")
    print("• 建议在虚拟环境中运行")
    print("• 操作前请确保有足够的磁盘空间")
    
    print("\n🔧 故障排除:")
    print("• 如果GUI无法启动，检查tkinter是否安装")
    print("• 如果解包失败，检查文件权限和磁盘空间")
    print("• 如果打包后大小差异很大，运行测试脚本诊断")
    
    print("\n📞 技术支持:")
    print("• 运行 'python analyze_cpio.py' 分析文件格式")
    print("• 运行 'python test_fix.py' 进行功能测试")
    print("• 查看日志输出获取详细错误信息")

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 6):
        print("❌ 错误: 需要Python 3.6或更高版本")
        return False
        
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要文件
    required_files = [
        "解包打包.py",
        "analyze_cpio.py", 
        "test_fix.py",
        "recovery/ramdisk.cpio"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
        else:
            print(f"✅ 找到文件: {file}")
            
    if missing_files:
        print("❌ 缺少以下文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
        
    # 检查tkinter
    try:
        import tkinter
        print("✅ tkinter可用")
    except ImportError:
        print("❌ tkinter不可用，GUI功能可能无法使用")
        
    print("✅ 环境检查完成")
    return True

if __name__ == "__main__":
    print("正在检查环境...")
    if check_environment():
        print("✅ 环境检查通过")
        main()
    else:
        print("❌ 环境检查失败，请解决上述问题后重试")
        sys.exit(1)
