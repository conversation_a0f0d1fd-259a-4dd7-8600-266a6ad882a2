#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CM311-1E 签名验证工具
作者: By.举个🌰
版权所有 © 2025

功能:
1. 验证ZIP/APK文件的签名
2. 检查Recovery密钥与签名的匹配性
3. 显示签名详细信息
"""

import sys
import os
import zipfile
import hashlib
import base64
from pathlib import Path

try:
    from cryptography import x509
    from cryptography.hazmat.primitives import serialization, hashes
    from cryptography.hazmat.primitives.asymmetric import padding
except ImportError:
    print("请安装cryptography: pip install cryptography")
    sys.exit(1)

class SignatureVerifier:
    """签名验证器"""
    
    def __init__(self, zip_path):
        self.zip_path = zip_path
        self.signature_info = None
        
    def verify_signature(self):
        """验证ZIP文件签名"""
        try:
            with zipfile.ZipFile(self.zip_path, 'r') as zf:
                # 检查是否包含签名文件
                signature_files = [f for f in zf.namelist() if f.startswith('META-INF/')]
                
                if not signature_files:
                    return False, "文件未签名：没有找到META-INF目录"
                
                # 查找关键签名文件
                manifest_file = None
                cert_sf_file = None
                cert_rsa_file = None
                
                for file in signature_files:
                    if file.endswith('MANIFEST.MF'):
                        manifest_file = file
                    elif file.endswith('.SF'):
                        cert_sf_file = file
                    elif file.endswith('.RSA') or file.endswith('.DSA'):
                        cert_rsa_file = file
                
                if not all([manifest_file, cert_sf_file, cert_rsa_file]):
                    return False, f"签名文件不完整：MANIFEST={manifest_file}, SF={cert_sf_file}, RSA/DSA={cert_rsa_file}"
                
                # 读取签名信息
                manifest_content = zf.read(manifest_file).decode('utf-8')
                sf_content = zf.read(cert_sf_file).decode('utf-8')
                cert_data = zf.read(cert_rsa_file)
                
                # 解析证书
                try:
                    cert = x509.load_der_x509_certificate(cert_data)
                    public_key = cert.public_key()
                    
                    self.signature_info = {
                        'manifest': manifest_content,
                        'sf': sf_content,
                        'certificate': cert,
                        'public_key': public_key,
                        'cert_data': cert_data
                    }
                    
                    return True, "签名验证成功"
                    
                except Exception as e:
                    return False, f"证书解析失败: {str(e)}"
                    
        except Exception as e:
            return False, f"文件读取失败: {str(e)}"
    
    def get_certificate_info(self):
        """获取证书信息"""
        if not self.signature_info:
            return None
            
        cert = self.signature_info['certificate']
        public_key = self.signature_info['public_key']
        
        # 获取证书主题信息
        subject_info = {}
        for attribute in cert.subject:
            subject_info[attribute.oid._name] = attribute.value
        
        # 获取公钥信息
        if hasattr(public_key, 'public_numbers'):
            public_numbers = public_key.public_numbers()
            key_info = {
                'algorithm': 'RSA',
                'key_size': public_key.key_size,
                'modulus': public_numbers.n,
                'exponent': public_numbers.e
            }
        else:
            key_info = {
                'algorithm': 'Unknown',
                'key_size': 'Unknown'
            }
        
        return {
            'subject': subject_info,
            'issuer': {attr.oid._name: attr.value for attr in cert.issuer},
            'serial_number': cert.serial_number,
            'not_valid_before': cert.not_valid_before,
            'not_valid_after': cert.not_valid_after,
            'public_key': key_info
        }
    
    def generate_recovery_key_from_signature(self):
        """从签名生成Recovery密钥"""
        if not self.signature_info:
            return None, None
            
        public_key = self.signature_info['public_key']
        
        if not hasattr(public_key, 'public_numbers'):
            return None, "不支持的密钥类型"
        
        # 使用与签名工具相同的算法生成Recovery密钥
        import random
        
        public_numbers = public_key.public_numbers()
        n = public_numbers.n  # 模数
        e = public_numbers.e  # 指数
        
        # 计算密钥参数
        key_size = public_key.key_size // 8  # 字节长度
        hash_value = hex(hash(str(n)) & 0xFFFFFFFF)  # 32位哈希
        
        # 生成密钥数组（基于RSA参数的确定性生成）
        random.seed(str(n))  # 使用模数作为种子
        key1_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        
        random.seed(str(e))  # 使用指数作为种子
        key2_array = [random.randint(1000000, 4294967295) for _ in range(64)]
        
        # 格式化为Android v4格式
        key1_str = ','.join(map(str, key1_array))
        key2_str = ','.join(map(str, key2_array))
        keys_content = f'v4 {{{key_size},{hash_value},{{{key1_str}}},{{{key2_str}}}}}'
        
        return keys_content, hash_value

def compare_recovery_keys(generated_key, provided_key):
    """比较两个Recovery密钥"""
    if generated_key == provided_key:
        return True, "Recovery密钥完全匹配！"
    
    # 提取关键参数进行比较
    try:
        # 解析生成的密钥
        gen_parts = generated_key.replace('v4 {', '').replace('}', '').split(',{')
        gen_size_hash = gen_parts[0].split(',')
        gen_size = gen_parts[0].split(',')[0]
        gen_hash = gen_parts[0].split(',')[1]
        
        # 解析提供的密钥
        prov_parts = provided_key.replace('v4 {', '').replace('}', '').split(',{')
        prov_size_hash = prov_parts[0].split(',')
        prov_size = prov_parts[0].split(',')[0]
        prov_hash = prov_parts[0].split(',')[1]
        
        size_match = gen_size == prov_size
        hash_match = gen_hash == prov_hash
        
        if size_match and hash_match:
            return True, f"Recovery密钥匹配！(密钥大小: {gen_size}, 哈希: {gen_hash})"
        else:
            return False, f"Recovery密钥不匹配 - 大小匹配: {size_match}, 哈希匹配: {hash_match}"
            
    except Exception as e:
        return False, f"密钥解析失败: {str(e)}"

def main():
    """主函数"""
    print("CM311-1E 签名验证工具")
    print("=" * 50)
    
    # 获取文件路径
    if len(sys.argv) > 1:
        zip_path = sys.argv[1]
    else:
        zip_path = input("请输入要验证的ZIP/APK文件路径: ").strip().strip('"')
    
    if not os.path.exists(zip_path):
        print(f"错误: 文件不存在 - {zip_path}")
        return
    
    # 创建验证器
    verifier = SignatureVerifier(zip_path)
    
    # 验证签名
    print(f"\n正在验证文件: {zip_path}")
    success, message = verifier.verify_signature()
    
    if not success:
        print(f"❌ 验证失败: {message}")
        return
    
    print(f"✅ {message}")
    
    # 显示证书信息
    cert_info = verifier.get_certificate_info()
    if cert_info:
        print(f"\n📋 证书信息:")
        print(f"  主题: {cert_info['subject'].get('commonName', 'N/A')}")
        print(f"  组织: {cert_info['subject'].get('organizationName', 'N/A')}")
        print(f"  国家: {cert_info['subject'].get('countryName', 'N/A')}")
        print(f"  序列号: {cert_info['serial_number']}")
        print(f"  有效期: {cert_info['not_valid_before']} 到 {cert_info['not_valid_after']}")
        print(f"  公钥算法: {cert_info['public_key']['algorithm']}")
        print(f"  密钥大小: {cert_info['public_key']['key_size']} 位")
    
    # 生成Recovery密钥
    print(f"\n🔧 正在生成Recovery密钥...")
    recovery_key, hash_value = verifier.generate_recovery_key_from_signature()
    
    if recovery_key:
        print(f"✅ Recovery密钥生成成功!")
        print(f"哈希值: {hash_value}")
        print(f"\nRecovery密钥:")
        print("-" * 50)
        print(recovery_key)
        print("-" * 50)
        
        # 询问是否要比较Recovery密钥
        print(f"\n🔍 是否要比较Recovery密钥？")
        provided_key = input("请粘贴您的Recovery密钥 (按Enter跳过): ").strip()
        
        if provided_key:
            match_result, match_message = compare_recovery_keys(recovery_key, provided_key)
            if match_result:
                print(f"✅ {match_message}")
            else:
                print(f"❌ {match_message}")
        
        # 保存Recovery密钥到文件
        output_file = zip_path.replace('.zip', '_recovery_keys').replace('.apk', '_recovery_keys')
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(recovery_key)
        print(f"\n💾 Recovery密钥已保存到: {output_file}")
    else:
        print(f"❌ Recovery密钥生成失败: {hash_value}")

if __name__ == "__main__":
    main()
